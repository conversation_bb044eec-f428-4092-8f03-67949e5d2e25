# Gemini API Service for Unity

This service provides a Unity-compatible wrapper for Google's Gemini API, allowing you to integrate advanced AI text generation capabilities into your Unity projects.

## Features

- **Multiple Model Support**: Works with `gemini-2.5-pro` and `gemini-2.5-flash`
- **System Instructions**: Set custom system prompts to guide AI behavior
- **Generation Configuration**: Control temperature, topP, topK, and stop sequences
- **Thinking Mode**: Enable advanced reasoning with configurable thinking budgets
- **Async/Await Support**: Uses Unity's `Awaitable` for modern async programming
- **Error Handling**: Comprehensive error logging and graceful failure handling
- **Helper Methods**: Convenient static methods for common use cases

## Setup

1. **Get API Key**: Obtain your Gemini API key from [Google AI Studio](https://aistudio.google.com/)
2. **Set API Key**: Configure your API key in the service or example script
3. **Choose Model**: Select between `gemini-2.5-pro` (more capable) or `gemini-2.5-flash` (faster)

## Basic Usage

### Simple Text Generation

```csharp
// Initialize the service
var geminiAPI = new GeminiAPI();
geminiAPI.ApiKey = "your-api-key-here";
geminiAPI.ApiUrl = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent";

// Create a simple request
var request = GeminiAPI.CreateSimpleTextRequest("Explain quantum physics in simple terms");
geminiAPI.RequestPayload = request;

// Generate content
var response = await geminiAPI.GenerateContent();
string generatedText = GeminiAPI.ExtractTextFromResponse(response);
Debug.Log(generatedText);
```

### With System Instructions

```csharp
var request = GeminiAPI.CreateRequestWithSystemInstruction(
    "You are a helpful physics teacher",
    "Explain quantum entanglement"
);
geminiAPI.RequestPayload = request;
var response = await geminiAPI.GenerateContent();
```

### Advanced Configuration

```csharp
var request = GeminiAPI.CreateRequestWithConfig(
    text: "Write a creative story",
    temperature: 1.2f,        // Higher = more creative
    topP: 0.9f,              // Nucleus sampling
    topK: 50,                // Top-k sampling
    stopSequences: new[] { "THE END" },
    thinkingBudget: 1024     // Enable thinking mode
);
```

## API Reference

### Main Classes

#### `GeminiAPI`
The main service class for interacting with the Gemini API.

**Properties:**
- `RequestPayload`: The request data to send
- `ApiKey`: Your Gemini API key
- `ApiUrl`: The API endpoint URL

**Methods:**
- `GenerateContent()`: Sends request and returns response
- `CreateSimpleTextRequest(string)`: Creates basic text request
- `CreateRequestWithSystemInstruction(string, string)`: Creates request with system prompt
- `CreateRequestWithConfig(...)`: Creates request with advanced configuration
- `ExtractTextFromResponse(GeminiResponse)`: Extracts text from API response

#### `GeminiRequest`
Contains the request payload structure.

**Properties:**
- `system_instruction`: Optional system instruction
- `contents`: Array of content parts (user messages)
- `generationConfig`: Configuration for text generation

#### `GenerationConfig`
Controls how the AI generates text.

**Properties:**
- `temperature`: Randomness (0.0-2.0, default: 1.0)
- `topP`: Nucleus sampling (0.0-1.0, default: 0.95)
- `topK`: Top-k sampling (default: 40)
- `stopSequences`: Array of stop sequences
- `thinkingConfig`: Configuration for thinking mode

#### `ThinkingConfig`
Controls the thinking/reasoning mode.

**Properties:**
- `thinkingBudget`: 
  - `0`: Thinking disabled
  - `-1`: Dynamic thinking budget
  - `>0`: Specific token budget for thinking

## Model Endpoints

### Gemini 2.5 Pro
- **URL**: `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent`
- **Use Case**: Complex reasoning, detailed analysis, high-quality content
- **Features**: Supports thinking mode for advanced reasoning

### Gemini 2.5 Flash
- **URL**: `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent`
- **Use Case**: Fast responses, simple tasks, real-time applications
- **Features**: Optimized for speed and efficiency

## Configuration Options

### Temperature
Controls randomness in generation:
- `0.0`: Deterministic, focused responses
- `1.0`: Balanced creativity and coherence
- `2.0`: Maximum creativity and randomness

### Top-P (Nucleus Sampling)
Controls diversity by considering only the top tokens that sum to P probability:
- `0.1`: Very focused, conservative responses
- `0.9`: More diverse, creative responses

### Top-K
Limits consideration to the top K most likely tokens:
- Lower values: More focused responses
- Higher values: More diverse responses

### Stop Sequences
Array of strings that will stop generation when encountered:
```csharp
stopSequences: new[] { "END", "STOP", "###" }
```

## Error Handling

The service includes comprehensive error handling:

```csharp
var response = await geminiAPI.GenerateContent();
if (response == null)
{
    Debug.LogError("Request failed - check logs for details");
    return;
}

string text = GeminiAPI.ExtractTextFromResponse(response);
if (string.IsNullOrEmpty(text))
{
    Debug.LogError("No text content in response");
    return;
}
```

## Best Practices

1. **API Key Security**: Never commit API keys to version control
2. **Rate Limiting**: Implement appropriate delays between requests
3. **Error Handling**: Always check for null responses
4. **Model Selection**: Use Flash for speed, Pro for quality
5. **Token Management**: Monitor usage through `response.usageMetadata`

## Example Integration

See `GeminiAPIExample.cs` for complete working examples including:
- Simple text generation
- System instruction usage
- Advanced configuration
- Thinking mode
- Custom request building

## Troubleshooting

### Common Issues

1. **"API key is null or empty"**: Set the ApiKey property
2. **"API URL is null or empty"**: Set the ApiUrl property with correct model
3. **"Request failed"**: Check API key validity and network connection
4. **"Failed to parse response"**: API response format may have changed

### Debug Information

Enable detailed logging by checking the Console for:
- Request payload JSON
- Response text
- Error messages
- Token usage statistics
