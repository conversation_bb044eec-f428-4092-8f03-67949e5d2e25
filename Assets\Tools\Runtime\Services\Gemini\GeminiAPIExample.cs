using UnityEngine;

namespace SmartVertex.Tools
{
    /// <summary>
    /// Example usage of the GeminiAPI service.
    /// </summary>
    public class GeminiAPIExample : MonoBehaviour
    {
        [Header("API Configuration")]
        [SerializeField] private string apiKey = "YOUR_GEMINI_API_KEY";
        [SerializeField] private string model = "gemini-2.5-flash"; // or "gemini-2.5-pro"
        
        [Header("Request Settings")]
        [SerializeField] private string userPrompt = "Explain how AI works";
        [SerializeField] private string systemInstruction = "You are a helpful AI assistant.";
        [SerializeField] private float temperature = 1.0f;
        [SerializeField] private float topP = 0.8f;
        [SerializeField] private int topK = 10;
        [SerializeField] private int thinkingBudget = 0; // 0=off, -1=dynamic, >0=specific budget

        private GeminiAPI geminiAPI;

        private void Start()
        {
            // Initialize the API service
            geminiAPI = new GeminiAPI();
            geminiAPI.ApiKey = apiKey;
            geminiAPI.ApiUrl = $"https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent";
        }

        /// <summary>
        /// Example: Simple text generation request.
        /// </summary>
        public async void GenerateSimpleText()
        {
            if (string.IsNullOrEmpty(apiKey) || apiKey == "YOUR_GEMINI_API_KEY")
            {
                Debug.LogError("Please set your Gemini API key!");
                return;
            }

            Debug.Log("Sending simple text request...");

            // Create a simple text request
            var request = GeminiAPI.CreateSimpleTextRequest(userPrompt);
            geminiAPI.RequestPayload = request;

            // Send the request
            var response = await geminiAPI.GenerateContent();
            
            if (response != null)
            {
                string generatedText = GeminiAPI.ExtractTextFromResponse(response);
                Debug.Log($"Generated Text: {generatedText}");
                
                // Log usage information
                if (response.usageMetadata != null)
                {
                    Debug.Log($"Tokens used - Prompt: {response.usageMetadata.promptTokenCount}, " +
                             $"Response: {response.usageMetadata.candidatesTokenCount}, " +
                             $"Total: {response.usageMetadata.totalTokenCount}");
                }
            }
            else
            {
                Debug.LogError("Failed to generate content");
            }
        }

        /// <summary>
        /// Example: Request with system instruction.
        /// </summary>
        public async void GenerateWithSystemInstruction()
        {
            if (string.IsNullOrEmpty(apiKey) || apiKey == "YOUR_GEMINI_API_KEY")
            {
                Debug.LogError("Please set your Gemini API key!");
                return;
            }

            Debug.Log("Sending request with system instruction...");

            // Create request with system instruction
            var request = GeminiAPI.CreateRequestWithSystemInstruction(systemInstruction, userPrompt);
            geminiAPI.RequestPayload = request;

            // Send the request
            var response = await geminiAPI.GenerateContent();
            
            if (response != null)
            {
                string generatedText = GeminiAPI.ExtractTextFromResponse(response);
                Debug.Log($"Generated Text with System Instruction: {generatedText}");
            }
            else
            {
                Debug.LogError("Failed to generate content with system instruction");
            }
        }

        /// <summary>
        /// Example: Request with advanced generation configuration.
        /// </summary>
        public async void GenerateWithAdvancedConfig()
        {
            if (string.IsNullOrEmpty(apiKey) || apiKey == "YOUR_GEMINI_API_KEY")
            {
                Debug.LogError("Please set your Gemini API key!");
                return;
            }

            Debug.Log("Sending request with advanced configuration...");

            // Create request with generation config
            string[] stopSequences = { "Title", "END" };
            var request = GeminiAPI.CreateRequestWithConfig(
                userPrompt, 
                temperature, 
                topP, 
                topK, 
                stopSequences, 
                thinkingBudget
            );
            
            geminiAPI.RequestPayload = request;

            // Send the request
            var response = await geminiAPI.GenerateContent();
            
            if (response != null)
            {
                string generatedText = GeminiAPI.ExtractTextFromResponse(response);
                Debug.Log($"Generated Text with Config: {generatedText}");
                
                // Log finish reason
                if (response.candidates != null && response.candidates.Length > 0)
                {
                    Debug.Log($"Finish Reason: {response.candidates[0].finishReason}");
                }
            }
            else
            {
                Debug.LogError("Failed to generate content with advanced config");
            }
        }

        /// <summary>
        /// Example: Request with thinking mode enabled.
        /// </summary>
        public async void GenerateWithThinking()
        {
            if (string.IsNullOrEmpty(apiKey) || apiKey == "YOUR_GEMINI_API_KEY")
            {
                Debug.LogError("Please set your Gemini API key!");
                return;
            }

            Debug.Log("Sending request with thinking mode...");

            // Create request with thinking enabled (1024 token budget)
            var request = GeminiAPI.CreateRequestWithConfig(
                "Solve this complex problem: What are the implications of quantum computing on cryptography?", 
                temperature: 0.7f,
                thinkingBudget: 1024 // Enable thinking with 1024 token budget
            );
            
            geminiAPI.RequestPayload = request;

            // Send the request
            var response = await geminiAPI.GenerateContent();
            
            if (response != null)
            {
                string generatedText = GeminiAPI.ExtractTextFromResponse(response);
                Debug.Log($"Generated Text with Thinking: {generatedText}");
            }
            else
            {
                Debug.LogError("Failed to generate content with thinking mode");
            }
        }

        /// <summary>
        /// Example: Custom request building for complex scenarios.
        /// </summary>
        public async void GenerateCustomRequest()
        {
            if (string.IsNullOrEmpty(apiKey) || apiKey == "YOUR_GEMINI_API_KEY")
            {
                Debug.LogError("Please set your Gemini API key!");
                return;
            }

            Debug.Log("Sending custom request...");

            // Build a completely custom request
            var customRequest = new GeminiAPI.GeminiRequest
            {
                system_instruction = new GeminiAPI.SystemInstruction
                {
                    parts = new GeminiAPI.Part[]
                    {
                        new GeminiAPI.Part { text = "You are a creative writing assistant specializing in science fiction." }
                    }
                },
                contents = new GeminiAPI.Content[]
                {
                    new GeminiAPI.Content
                    {
                        parts = new GeminiAPI.Part[]
                        {
                            new GeminiAPI.Part { text = "Write a short story about time travel in exactly 100 words." }
                        }
                    }
                },
                generationConfig = new GeminiAPI.GenerationConfig
                {
                    temperature = 1.2f,
                    topP = 0.9f,
                    topK = 50,
                    stopSequences = new string[] { "THE END" }
                }
            };

            geminiAPI.RequestPayload = customRequest;

            // Send the request
            var response = await geminiAPI.GenerateContent();
            
            if (response != null)
            {
                string generatedText = GeminiAPI.ExtractTextFromResponse(response);
                Debug.Log($"Custom Generated Story: {generatedText}");
            }
            else
            {
                Debug.LogError("Failed to generate custom content");
            }
        }
    }
}
