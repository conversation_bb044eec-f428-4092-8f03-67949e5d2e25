using System;
using System.Text;
using UnityEngine;
using UnityEngine.Networking;

namespace SmartVertex.Tools
{
    /// <summary>
    /// <PERSON>les sending text generation requests to the Google Gemini API and retrieving responses.
    /// </summary>
    public class GeminiAPI
    {
        private GeminiRequest requestPayload;
        private string apiKey;
        private string apiUrl;

        /// <summary>
        /// Gets or sets the Gemini request payload.
        /// </summary>
        public GeminiRequest RequestPayload
        {
            get => requestPayload;
            set => requestPayload = value;
        }

        /// <summary>
        /// Gets or sets the API key.
        /// </summary>
        public string ApiKey
        {
            get => apiKey;
            set => apiKey = value;
        }

        /// <summary>
        /// Gets or sets the API URL.
        /// </summary>
        public string ApiUrl
        {
            get => apiUrl;
            set => apiUrl = value;
        }

        /// <summary>
        /// Data container for Gemini API request payload.
        /// </summary>
        [Serializable]
        public class GeminiRequest
        {
            public SystemInstruction system_instruction;
            public Content[] contents;
            public GenerationConfig generationConfig;
        }

        /// <summary>
        /// System instruction for the AI model.
        /// </summary>
        [Serializable]
        public class SystemInstruction
        {
            public Part[] parts;
        }

        /// <summary>
        /// Content container for messages.
        /// </summary>
        [Serializable]
        public class Content
        {
            public Part[] parts;
        }

        /// <summary>
        /// Individual part of content (text, etc.).
        /// </summary>
        [Serializable]
        public class Part
        {
            public string text;
        }

        /// <summary>
        /// Configuration for text generation.
        /// </summary>
        [Serializable]
        public class GenerationConfig
        {
            public string[] stopSequences;
            public float temperature = 1.0f;
            public float topP = 0.95f;
            public int topK = 40;
            public ThinkingConfig thinkingConfig;
        }

        /// <summary>
        /// Configuration for thinking mode.
        /// </summary>
        [Serializable]
        public class ThinkingConfig
        {
            public int thinkingBudget = 0; // 0 = off, -1 = dynamic, >0 = specific budget
        }

        /// <summary>
        /// Response from Gemini API.
        /// </summary>
        [Serializable]
        public class GeminiResponse
        {
            public Candidate[] candidates;
            public UsageMetadata usageMetadata;
        }

        /// <summary>
        /// Individual candidate response.
        /// </summary>
        [Serializable]
        public class Candidate
        {
            public Content content;
            public string finishReason;
            public int index;
            public SafetyRating[] safetyRatings;
        }

        /// <summary>
        /// Usage metadata for the request.
        /// </summary>
        [Serializable]
        public class UsageMetadata
        {
            public int promptTokenCount;
            public int candidatesTokenCount;
            public int totalTokenCount;
        }

        /// <summary>
        /// Safety rating information.
        /// </summary>
        [Serializable]
        public class SafetyRating
        {
            public string category;
            public string probability;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="GeminiAPI"/> class without parameters.
        /// </summary>
        public GeminiAPI() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="GeminiAPI"/> class with a request payload.
        /// </summary>
        public GeminiAPI(GeminiRequest requestPayload)
        {
            this.requestPayload = requestPayload;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="GeminiAPI"/> class with a request payload and API key.
        /// </summary>
        public GeminiAPI(GeminiRequest requestPayload, string apiKey)
        {
            this.requestPayload = requestPayload;
            this.apiKey = apiKey;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="GeminiAPI"/> class with a request payload, API key, and API URL.
        /// </summary>
        public GeminiAPI(GeminiRequest requestPayload, string apiKey, string apiUrl)
        {
            this.requestPayload = requestPayload;
            this.apiKey = apiKey;
            this.apiUrl = apiUrl;
        }

        /// <summary>
        /// Generates content from the Gemini API asynchronously.
        /// </summary>
        /// <returns>The generated response, or null on failure.</returns>
        public async Awaitable<GeminiResponse> GenerateContent()
        {
            string responseText = await SendGeminiRequestAsync();
            if (string.IsNullOrEmpty(responseText))
                return null;

            try
            {
                return JsonUtility.FromJson<GeminiResponse>(responseText);
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to parse Gemini response: {ex.Message}");
                Debug.LogError($"Response text: {responseText}");
                return null;
            }
        }

        /// <summary>
        /// Sends the request to the Gemini API and returns the response text.
        /// </summary>
        /// <returns>The response text from the API, or null on failure.</returns>
        private async Awaitable<string> SendGeminiRequestAsync()
        {
            if (requestPayload == null)
            {
                Debug.LogError("Request payload is null");
                return null;
            }

            if (string.IsNullOrEmpty(apiKey))
            {
                Debug.LogError("API key is null or empty");
                return null;
            }

            if (string.IsNullOrEmpty(apiUrl))
            {
                Debug.LogError("API URL is null or empty");
                return null;
            }

            string jsonPayload = JsonUtility.ToJson(requestPayload);
            byte[] bodyRaw = Encoding.UTF8.GetBytes(jsonPayload);

            using (var request = new UnityWebRequest(apiUrl, "POST"))
            {
                request.uploadHandler = new UploadHandlerRaw(bodyRaw);
                request.downloadHandler = new DownloadHandlerBuffer();
                request.SetRequestHeader("Content-Type", "application/json");
                request.SetRequestHeader("x-goog-api-key", apiKey);

                await request.SendWebRequest();

                if (request.result == UnityWebRequest.Result.Success)
                {
                    return request.downloadHandler.text;
                }
                else
                {
                    Debug.LogError($"Gemini API request failed: {request.error}");
                    Debug.LogError($"Response: {request.downloadHandler.text}");
                    return null;
                }
            }
        }

        /// <summary>
        /// Creates a simple text request for content generation.
        /// </summary>
        /// <param name="text">The text prompt to send to Gemini.</param>
        /// <returns>A configured GeminiRequest object.</returns>
        public static GeminiRequest CreateSimpleTextRequest(string text)
        {
            return new GeminiRequest
            {
                contents = new Content[]
                {
                    new Content
                    {
                        parts = new Part[]
                        {
                            new Part { text = text }
                        }
                    }
                }
            };
        }

        /// <summary>
        /// Creates a request with system instruction and user text.
        /// </summary>
        /// <param name="systemInstruction">The system instruction for the AI.</param>
        /// <param name="userText">The user's text prompt.</param>
        /// <returns>A configured GeminiRequest object.</returns>
        public static GeminiRequest CreateRequestWithSystemInstruction(string systemInstruction, string userText)
        {
            return new GeminiRequest
            {
                system_instruction = new SystemInstruction
                {
                    parts = new Part[]
                    {
                        new Part { text = systemInstruction }
                    }
                },
                contents = new Content[]
                {
                    new Content
                    {
                        parts = new Part[]
                        {
                            new Part { text = userText }
                        }
                    }
                }
            };
        }

        /// <summary>
        /// Creates a request with generation configuration.
        /// </summary>
        /// <param name="text">The text prompt to send to Gemini.</param>
        /// <param name="temperature">Controls randomness (0.0 to 2.0).</param>
        /// <param name="topP">Controls nucleus sampling (0.0 to 1.0).</param>
        /// <param name="topK">Controls top-k sampling.</param>
        /// <param name="stopSequences">Optional stop sequences.</param>
        /// <param name="thinkingBudget">Thinking budget (0=off, -1=dynamic, >0=specific).</param>
        /// <returns>A configured GeminiRequest object.</returns>
        public static GeminiRequest CreateRequestWithConfig(string text, float temperature = 1.0f,
            float topP = 0.95f, int topK = 40, string[] stopSequences = null, int thinkingBudget = 0)
        {
            var request = new GeminiRequest
            {
                contents = new Content[]
                {
                    new Content
                    {
                        parts = new Part[]
                        {
                            new Part { text = text }
                        }
                    }
                },
                generationConfig = new GenerationConfig
                {
                    temperature = temperature,
                    topP = topP,
                    topK = topK,
                    stopSequences = stopSequences
                }
            };

            if (thinkingBudget != 0)
            {
                request.generationConfig.thinkingConfig = new ThinkingConfig
                {
                    thinkingBudget = thinkingBudget
                };
            }

            return request;
        }

        /// <summary>
        /// Extracts the text content from the first candidate in the response.
        /// </summary>
        /// <param name="response">The Gemini API response.</param>
        /// <returns>The generated text, or null if not available.</returns>
        public static string ExtractTextFromResponse(GeminiResponse response)
        {
            if (response?.candidates == null || response.candidates.Length == 0)
                return null;

            var firstCandidate = response.candidates[0];
            if (firstCandidate?.content?.parts == null || firstCandidate.content.parts.Length == 0)
                return null;

            return firstCandidate.content.parts[0]?.text;
        }
    }
}